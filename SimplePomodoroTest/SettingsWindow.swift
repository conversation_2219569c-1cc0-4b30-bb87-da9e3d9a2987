import Cocoa

// Кастомная ячейка для вертикального центрирования текста
class CenteredTextFieldCell: NSTextFieldCell {
    override func drawingRect(forBounds rect: NSRect) -> NSRect {
        let drawingRect = super.drawingRect(forBounds: rect)
        let textSize = self.cellSize(forBounds: rect)
        let heightDiff = drawingRect.height - textSize.height
        if heightDiff > 0 {
            var newRect = drawingRect
            newRect.origin.y += heightDiff / 2
            newRect.size.height = textSize.height
            return newRect
        }
        return drawingRect
    }
}

// Кастомный TextField с правильным вертикальным выравниванием
class CenteredTextField: NSTextField {
    override func awakeFromNib() {
        super.awakeFromNib()
        self.cell = CenteredTextFieldCell()
    }

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        self.cell = CenteredTextFieldCell()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        self.cell = CenteredTextFieldCell()
    }
}

// Кастомный компонент как на iPhone с кнопками + и -
class NumberStepperView: NSView {
    private var valueLabel: NSTextField!
    private var minusButton: NSButton!
    private var plusButton: NSButton!

    private var currentValue: Int = 0
    private var minValue: Int = 1
    private var maxValue: Int = 999

    var onValueChanged: ((Int) -> Void)?

    init(value: Int, minValue: Int = 1, maxValue: Int = 999) {
        super.init(frame: .zero)
        self.currentValue = value
        self.minValue = minValue
        self.maxValue = maxValue
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        // Контейнер с красивым фоном
        self.wantsLayer = true
        self.layer?.backgroundColor = NSColor(red: 0.18, green: 0.18, blue: 0.18, alpha: 1.0).cgColor
        self.layer?.cornerRadius = 8
        self.layer?.borderWidth = 1
        self.layer?.borderColor = NSColor(red: 0.35, green: 0.35, blue: 0.35, alpha: 1.0).cgColor

        // Кнопка минус
        minusButton = NSButton()
        minusButton.title = "−"
        minusButton.font = NSFont.systemFont(ofSize: 18, weight: .medium)
        minusButton.bezelStyle = .regularSquare
        minusButton.isBordered = false
        minusButton.wantsLayer = true
        minusButton.layer?.backgroundColor = NSColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0).cgColor
        minusButton.layer?.cornerRadius = 6
        minusButton.target = self
        minusButton.action = #selector(decreaseValue)

        // Label со значением
        valueLabel = NSTextField(labelWithString: String(currentValue))
        valueLabel.font = NSFont.systemFont(ofSize: 16, weight: .medium)  // Увеличили размер шрифта
        valueLabel.textColor = NSColor.white
        valueLabel.alignment = .center
        valueLabel.backgroundColor = NSColor.clear

        // Кнопка плюс
        plusButton = NSButton()
        plusButton.title = "+"
        plusButton.font = NSFont.systemFont(ofSize: 18, weight: .medium)
        plusButton.bezelStyle = .regularSquare
        plusButton.isBordered = false
        plusButton.wantsLayer = true
        plusButton.layer?.backgroundColor = NSColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0).cgColor
        plusButton.layer?.cornerRadius = 6
        plusButton.target = self
        plusButton.action = #selector(increaseValue)

        // Добавляем элементы
        addSubview(minusButton)
        addSubview(valueLabel)
        addSubview(plusButton)

        // Auto Layout
        minusButton.translatesAutoresizingMaskIntoConstraints = false
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        plusButton.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // Кнопка минус (меньше размер)
            minusButton.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 4),
            minusButton.centerYAnchor.constraint(equalTo: centerYAnchor),
            minusButton.widthAnchor.constraint(equalToConstant: 20),
            minusButton.heightAnchor.constraint(equalToConstant: 20),

            // Label по центру с меньшими отступами
            valueLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            valueLabel.centerYAnchor.constraint(equalTo: centerYAnchor),
            valueLabel.leadingAnchor.constraint(greaterThanOrEqualTo: minusButton.trailingAnchor, constant: 8),
            valueLabel.trailingAnchor.constraint(lessThanOrEqualTo: plusButton.leadingAnchor, constant: -8),
            valueLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 50),  // Уменьшили до разумного

            // Кнопка плюс (меньше размер)
            plusButton.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -4),
            plusButton.centerYAnchor.constraint(equalTo: centerYAnchor),
            plusButton.widthAnchor.constraint(equalToConstant: 20),
            plusButton.heightAnchor.constraint(equalToConstant: 20)
        ])

        updateButtonStates()
    }

    @objc private func decreaseValue() {
        if currentValue > minValue {
            currentValue -= 1
            updateValue()
        }
    }

    @objc private func increaseValue() {
        if currentValue < maxValue {
            currentValue += 1
            updateValue()
        }
    }

    private func updateValue() {
        valueLabel.stringValue = String(currentValue)
        updateButtonStates()
        onValueChanged?(currentValue)
    }

    private func updateButtonStates() {
        minusButton.isEnabled = currentValue > minValue
        plusButton.isEnabled = currentValue < maxValue

        minusButton.alphaValue = minusButton.isEnabled ? 1.0 : 0.5
        plusButton.alphaValue = plusButton.isEnabled ? 1.0 : 0.5
    }

    func setValue(_ value: Int) {
        currentValue = max(minValue, min(maxValue, value))
        valueLabel.stringValue = String(currentValue)
        updateButtonStates()
    }

    func getValue() -> Int {
        return currentValue
    }

    func setEnabled(_ enabled: Bool) {
        minusButton.isEnabled = enabled && currentValue > minValue
        plusButton.isEnabled = enabled && currentValue < maxValue

        if !enabled {
            minusButton.alphaValue = 0.3
            plusButton.alphaValue = 0.3
            valueLabel.alphaValue = 0.3  // Сделали текст заметно серее
        } else {
            updateButtonStates()
            valueLabel.alphaValue = 1.0
        }
    }
}

class SettingsWindow: NSWindow {

    // Поля для настроек времени
    private var workDurationField: NumberStepperView!
    private var shortBreakDurationField: NumberStepperView!
    private var longBreakDurationField: NumberStepperView!
    private var longBreakStartTimeField: NSView!  // Контейнер с TextField
    private var longBreakEndTimeField: NSView!    // Контейнер с TextField
    private var dailyIntervalsField: NumberStepperView!

    // Общие настройки
    private var launchAtLoginSwitch: NSSwitch!
    private var languagePopUpButton: NSPopUpButton!
    private var adaptiveChangesSwitch: NSSwitch!

    // Дни недели
    private var weekdayButtons: [NSButton] = []

    // Звуковые настройки
    private var sessionSoundSwitch: NSSwitch!
    private var sessionSoundPopUp: NSPopUpButton!
    private var breakSoundSwitch: NSSwitch!
    private var breakSoundPopUp: NSPopUpButton!
    private var soundVolumeSlider: NSSlider!
    private var soundVolumeLabel: NSTextField!
    private var soundManager: SoundManager!

    // Настройки неформальных сессий
    private var informalSessionSwitch: NSSwitch!

    // Приоритетный проект (Ichiban)
    private var priorityProjectPopUp: NSPopUpButton!
    private var projectManager: ProjectManager?

    private var onSettingsChanged: ((TimeInterval) -> Void)?

    init() {
        // Создаем окно настроек (стандартная ширина для всех вкладок)
        let windowRect = NSRect(x: 0, y: 0, width: 750, height: 600)

        super.init(
            contentRect: windowRect,
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        // Принудительно устанавливаем размер сразу после создания
        self.setContentSize(NSSize(width: 750, height: 600))

        // Инициализируем SoundManager
        soundManager = SoundManager()

        setupWindow()
        setupUI()
    }

    /// Устанавливает ProjectManager для работы с приоритетными проектами
    func setProjectManager(_ projectManager: ProjectManager) {
        self.projectManager = projectManager
        updatePriorityProjectPopUp()
    }

    private func setupWindow() {
        self.title = "Настройки"
        self.isReleasedWhenClosed = false

        // Устанавливаем фиксированный размер окна
        let fixedSize = NSSize(width: 750, height: 600)
        self.setContentSize(fixedSize)
        self.minSize = fixedSize
        self.maxSize = fixedSize

        // Отключаем возможность изменения размера
        self.styleMask.remove(.resizable)

        self.center()

        // Делаем окно всегда поверх других
        self.level = .floating

        // Устанавливаем темный фон
        if let contentView = self.contentView {
            contentView.wantsLayer = true
            contentView.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        }
    }
    
    private func setupUI() {
        let contentView = NSView(frame: self.contentRect(forFrameRect: self.frame))
        self.contentView = contentView

        // Устанавливаем темный фон
        contentView.wantsLayer = true
        contentView.layer?.backgroundColor = NSColor(red: 0.15, green: 0.15, blue: 0.15, alpha: 1.0).cgColor

        // Создаем контейнер для вкладок
        let tabView = NSTabView()
        tabView.tabViewType = .topTabsBezelBorder
        tabView.translatesAutoresizingMaskIntoConstraints = false
        tabView.autoresizingMask = []  // Отключаем автоматическое изменение размера
        contentView.addSubview(tabView)

        // Создаем вкладки
        setupGeneralTab(tabView)
        setupTimingTab(tabView)
        setupAdvancedTab(tabView)
        setupTemporaryTab(tabView)  // НОВАЯ ВКЛАДКА

        // Constraints для tabView с фиксированным размером
        NSLayoutConstraint.activate([
            tabView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            tabView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            tabView.widthAnchor.constraint(equalToConstant: 710),  // Фиксированная ширина
            tabView.heightAnchor.constraint(equalToConstant: 560)  // Фиксированная высота
        ])

        // Принудительно устанавливаем размер после создания всех элементов
        self.setContentSize(NSSize(width: 750, height: 600))

        DispatchQueue.main.async {
            self.setContentSize(NSSize(width: 750, height: 600))
        }

        // Еще раз через небольшую задержку
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.setContentSize(NSSize(width: 750, height: 600))
        }
    }

    // MARK: - Tab Setup Methods

    private func setupGeneralTab(_ tabView: NSTabView) {
        let tabViewItem = NSTabViewItem()
        tabViewItem.label = "Общие"

        // Добавляем иконку для вкладки
        if let image = NSImage(systemSymbolName: "gearshape", accessibilityDescription: nil) {
            let config = NSImage.SymbolConfiguration(pointSize: 14, weight: .medium)
            tabViewItem.image = image.withSymbolConfiguration(config)
        }

        let contentView = NSView()
        contentView.wantsLayer = true
        contentView.layer?.backgroundColor = NSColor(red: 0.15, green: 0.15, blue: 0.15, alpha: 1.0).cgColor

        // Настройка языка
        let languageContainer = createSettingContainer(
            iconName: "globe",
            iconColor: NSColor.systemBlue,
            title: "Язык интерфейса",
            subtitle: "Выберите язык приложения"
        )

        languagePopUpButton = NSPopUpButton()
        languagePopUpButton.addItems(withTitles: ["Русский", "English"])
        languagePopUpButton.selectItem(at: 0) // Русский по умолчанию
        languagePopUpButton.translatesAutoresizingMaskIntoConstraints = false
        languageContainer.addSubview(languagePopUpButton)

        // Настройка автозапуска
        let launchAtLoginContainer = createSettingContainer(
            iconName: "power",
            iconColor: NSColor.systemGreen,
            title: "Запускать при старте macOS",
            subtitle: "Автоматически запускать uProd при входе в систему"
        )

        launchAtLoginSwitch = NSSwitch()
        launchAtLoginSwitch.state = LaunchAtLoginManager.shared.isEnabled ? .on : .off
        launchAtLoginSwitch.target = self
        launchAtLoginSwitch.action = #selector(launchAtLoginChanged)
        launchAtLoginSwitch.translatesAutoresizingMaskIntoConstraints = false

        // Принудительно обновляем внешний вид переключателя
        DispatchQueue.main.async {
            self.launchAtLoginSwitch.needsDisplay = true
        }

        launchAtLoginContainer.addSubview(launchAtLoginSwitch)

        // Разделитель перед приоритетным проектом
        let separatorView = NSView()
        separatorView.translatesAutoresizingMaskIntoConstraints = false
        separatorView.wantsLayer = true
        separatorView.layer?.backgroundColor = NSColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0).cgColor

        // Приоритетный проект (Ichiban)
        let priorityProjectContainer = createPriorityProjectContainer()

        contentView.addSubview(languageContainer)
        contentView.addSubview(launchAtLoginContainer)
        contentView.addSubview(separatorView)
        contentView.addSubview(priorityProjectContainer)

        // Constraints
        NSLayoutConstraint.activate([
            languageContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 32),
            languageContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            languageContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            languageContainer.heightAnchor.constraint(equalToConstant: 60),

            languagePopUpButton.centerYAnchor.constraint(equalTo: languageContainer.centerYAnchor),
            languagePopUpButton.trailingAnchor.constraint(equalTo: languageContainer.trailingAnchor, constant: -16),
            languagePopUpButton.widthAnchor.constraint(equalToConstant: 120),

            launchAtLoginContainer.topAnchor.constraint(equalTo: languageContainer.bottomAnchor, constant: 16),
            launchAtLoginContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            launchAtLoginContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            launchAtLoginContainer.heightAnchor.constraint(equalToConstant: 60),

            launchAtLoginSwitch.centerYAnchor.constraint(equalTo: launchAtLoginContainer.centerYAnchor),
            launchAtLoginSwitch.trailingAnchor.constraint(equalTo: launchAtLoginContainer.trailingAnchor, constant: -16),

            separatorView.topAnchor.constraint(equalTo: launchAtLoginContainer.bottomAnchor, constant: 24),
            separatorView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            separatorView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            separatorView.heightAnchor.constraint(equalToConstant: 1),

            priorityProjectContainer.topAnchor.constraint(equalTo: separatorView.bottomAnchor, constant: 24),
            priorityProjectContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            priorityProjectContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            priorityProjectContainer.heightAnchor.constraint(equalToConstant: 60)
        ])

        tabViewItem.view = contentView
        tabView.addTabViewItem(tabViewItem)

        // Принудительно устанавливаем размер для первой вкладки
        DispatchQueue.main.async {
            self.setContentSize(NSSize(width: 750, height: 600))
        }
    }

    private func setupTimingTab(_ tabView: NSTabView) {
        let tabViewItem = NSTabViewItem()
        tabViewItem.label = "Время"

        // Добавляем иконку для вкладки
        if let image = NSImage(systemSymbolName: "clock", accessibilityDescription: nil) {
            let config = NSImage.SymbolConfiguration(pointSize: 14, weight: .medium)
            tabViewItem.image = image.withSymbolConfiguration(config)
        }

        // Создаем основной контейнер для содержимого
        let contentView = NSView()
        contentView.wantsLayer = true
        contentView.layer?.backgroundColor = NSColor(red: 0.15, green: 0.15, blue: 0.15, alpha: 1.0).cgColor

        // Адаптивные изменения (перенесено сюда)
        let adaptiveContainer = createSettingContainer(
            iconName: "brain.head.profile",
            iconColor: NSColor.systemPurple,
            title: "Адаптивные изменения",
            subtitle: "Приложение анализирует ваше поведение и автоматически адаптирует размеры интервалов и отдыхов под вас"
        )

        adaptiveChangesSwitch = NSSwitch()
        adaptiveChangesSwitch.state = .on // Включено по умолчанию
        adaptiveChangesSwitch.target = self
        adaptiveChangesSwitch.action = #selector(adaptiveChangesToggled)
        adaptiveChangesSwitch.translatesAutoresizingMaskIntoConstraints = false

        // Принудительно обновляем внешний вид переключателя
        DispatchQueue.main.async {
            self.adaptiveChangesSwitch.needsDisplay = true
        }

        adaptiveContainer.addSubview(adaptiveChangesSwitch)

        // Длительность работы
        let workContainer = createSettingContainer(
            iconName: "timer",
            iconColor: NSColor.systemOrange,
            title: "Длительность работы",
            subtitle: "Время рабочего интервала в минутах"
        )

        workDurationField = createNumberStepper(value: 25, minValue: 1, maxValue: 120)  // Фиксированное значение
        workContainer.addSubview(workDurationField)

        // Короткий отдых
        let shortBreakContainer = createSettingContainer(
            iconName: "cup.and.saucer",
            iconColor: NSColor.systemBlue,
            title: "Короткий отдых",
            subtitle: "Длительность короткого перерыва в минутах"
        )

        shortBreakDurationField = createNumberStepper(value: 17, minValue: 1, maxValue: 60)
        shortBreakContainer.addSubview(shortBreakDurationField)

        // Длинный отдых
        let longBreakContainer = createSettingContainer(
            iconName: "bed.double",
            iconColor: NSColor.systemPurple,
            title: "Длинный отдых",
            subtitle: "Длительность длинного перерыва в минутах"
        )

        longBreakDurationField = createNumberStepper(value: 90, minValue: 1, maxValue: 180)
        longBreakContainer.addSubview(longBreakDurationField)

        // Количество интервалов в день
        let dailyIntervalsContainer = createSettingContainer(
            iconName: "calendar.badge.clock",
            iconColor: NSColor.systemTeal,
            title: "Интервалов в день",
            subtitle: "Целевое количество рабочих интервалов"
        )

        dailyIntervalsField = createNumberStepper(value: 8, minValue: 1, maxValue: 20)
        dailyIntervalsContainer.addSubview(dailyIntervalsField)

        // Время длинного интервала (перенесено из третьей вкладки)
        let timeRangeContainer = createSettingContainer(
            iconName: "clock.arrow.2.circlepath",
            iconColor: NSColor.systemIndigo,
            title: "Время длинного интервала",
            subtitle: "Примерное время для длинного рабочего блока"
        )

        let timeStackView = NSStackView()
        timeStackView.orientation = .horizontal
        timeStackView.spacing = 8
        timeStackView.translatesAutoresizingMaskIntoConstraints = false

        longBreakStartTimeField = createTextField(value: "11:00")
        longBreakStartTimeField.widthAnchor.constraint(equalToConstant: 60).isActive = true

        let dashLabel = NSTextField(labelWithString: "—")
        dashLabel.textColor = NSColor.white
        dashLabel.backgroundColor = NSColor.clear
        dashLabel.isBordered = false
        dashLabel.isEditable = false

        longBreakEndTimeField = createTextField(value: "13:00")
        longBreakEndTimeField.widthAnchor.constraint(equalToConstant: 60).isActive = true

        timeStackView.addArrangedSubview(longBreakStartTimeField)
        timeStackView.addArrangedSubview(dashLabel)
        timeStackView.addArrangedSubview(longBreakEndTimeField)

        timeRangeContainer.addSubview(timeStackView)

        // Рабочие дни
        let workdaysContainer = createWorkdaysContainer()

        contentView.addSubview(adaptiveContainer)
        contentView.addSubview(workContainer)
        contentView.addSubview(shortBreakContainer)
        contentView.addSubview(longBreakContainer)
        contentView.addSubview(dailyIntervalsContainer)
        contentView.addSubview(timeRangeContainer)
        contentView.addSubview(workdaysContainer)

        // Constraints
        NSLayoutConstraint.activate([
            adaptiveContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            adaptiveContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            adaptiveContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            adaptiveContainer.heightAnchor.constraint(equalToConstant: 70),

            adaptiveChangesSwitch.centerYAnchor.constraint(equalTo: adaptiveContainer.centerYAnchor),
            adaptiveChangesSwitch.trailingAnchor.constraint(equalTo: adaptiveContainer.trailingAnchor, constant: -16),

            workContainer.topAnchor.constraint(equalTo: adaptiveContainer.bottomAnchor, constant: 12),
            workContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            workContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            workContainer.heightAnchor.constraint(equalToConstant: 60),

            workDurationField.centerYAnchor.constraint(equalTo: workContainer.centerYAnchor),
            workDurationField.trailingAnchor.constraint(equalTo: workContainer.trailingAnchor, constant: -16),

            shortBreakContainer.topAnchor.constraint(equalTo: workContainer.bottomAnchor, constant: 8),
            shortBreakContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            shortBreakContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            shortBreakContainer.heightAnchor.constraint(equalToConstant: 60),

            shortBreakDurationField.centerYAnchor.constraint(equalTo: shortBreakContainer.centerYAnchor),
            shortBreakDurationField.trailingAnchor.constraint(equalTo: shortBreakContainer.trailingAnchor, constant: -16),

            longBreakContainer.topAnchor.constraint(equalTo: shortBreakContainer.bottomAnchor, constant: 8),
            longBreakContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            longBreakContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            longBreakContainer.heightAnchor.constraint(equalToConstant: 60),

            longBreakDurationField.centerYAnchor.constraint(equalTo: longBreakContainer.centerYAnchor),
            longBreakDurationField.trailingAnchor.constraint(equalTo: longBreakContainer.trailingAnchor, constant: -16),

            dailyIntervalsContainer.topAnchor.constraint(equalTo: longBreakContainer.bottomAnchor, constant: 8),
            dailyIntervalsContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            dailyIntervalsContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            dailyIntervalsContainer.heightAnchor.constraint(equalToConstant: 60),

            dailyIntervalsField.centerYAnchor.constraint(equalTo: dailyIntervalsContainer.centerYAnchor),
            dailyIntervalsField.trailingAnchor.constraint(equalTo: dailyIntervalsContainer.trailingAnchor, constant: -16),

            timeRangeContainer.topAnchor.constraint(equalTo: dailyIntervalsContainer.bottomAnchor, constant: 8),
            timeRangeContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            timeRangeContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            timeRangeContainer.heightAnchor.constraint(equalToConstant: 60),

            timeStackView.centerYAnchor.constraint(equalTo: timeRangeContainer.centerYAnchor),
            timeStackView.trailingAnchor.constraint(equalTo: timeRangeContainer.trailingAnchor, constant: -16),

            workdaysContainer.topAnchor.constraint(equalTo: timeRangeContainer.bottomAnchor, constant: 8),
            workdaysContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            workdaysContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            workdaysContainer.heightAnchor.constraint(equalToConstant: 70)
        ])

        // Устанавливаем правильное начальное состояние полей
        // Добавляем небольшую задержку чтобы все поля точно инициализировались
        DispatchQueue.main.async {
            self.adaptiveChangesToggled()

            // Принудительно обновляем состояние всех полей
            let adaptiveEnabled = self.adaptiveChangesSwitch.state == .on
            let fieldsEnabled = !adaptiveEnabled

            self.workDurationField.setEnabled(fieldsEnabled)
            self.shortBreakDurationField.setEnabled(fieldsEnabled)
            self.longBreakDurationField.setEnabled(fieldsEnabled)
            self.dailyIntervalsField.setEnabled(fieldsEnabled)
        }

        tabViewItem.view = contentView
        tabView.addTabViewItem(tabViewItem)

        // Добавляем наблюдатель для обновления переключателей при изменении фокуса
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(windowDidBecomeKey),
            name: NSWindow.didBecomeKeyNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(windowDidResignKey),
            name: NSWindow.didResignKeyNotification,
            object: nil
        )
    }

    private func setupAdvancedTab(_ tabView: NSTabView) {
        let tabViewItem = NSTabViewItem()
        tabViewItem.label = "Расширенные"

        // Добавляем иконку для вкладки
        if let image = NSImage(systemSymbolName: "slider.horizontal.3", accessibilityDescription: nil) {
            let config = NSImage.SymbolConfiguration(pointSize: 14, weight: .medium)
            tabViewItem.image = image.withSymbolConfiguration(config)
        }

        let contentView = NSView()
        contentView.wantsLayer = true
        contentView.layer?.backgroundColor = NSColor(red: 0.15, green: 0.15, blue: 0.15, alpha: 1.0).cgColor

        // Настройки звуков для завершения сессий
        let sessionSoundContainer = createSettingContainer(
            iconName: "speaker.wave.2",
            iconColor: NSColor.systemOrange,
            title: "Звук завершения сессии",
            subtitle: "Воспроизводить звук при завершении рабочего интервала"
        )

        sessionSoundSwitch = NSSwitch()
        sessionSoundSwitch.state = SoundSettings.shared.isSessionSoundEnabled ? .on : .off
        sessionSoundSwitch.target = self
        sessionSoundSwitch.action = #selector(sessionSoundToggled)
        sessionSoundSwitch.translatesAutoresizingMaskIntoConstraints = false

        sessionSoundPopUp = NSPopUpButton()
        sessionSoundPopUp.translatesAutoresizingMaskIntoConstraints = false
        setupSoundPopUpButton(sessionSoundPopUp, selectedSound: SoundSettings.shared.selectedSessionSound)
        sessionSoundPopUp.target = self
        sessionSoundPopUp.action = #selector(sessionSoundChanged)

        let sessionSoundStackView = NSStackView()
        sessionSoundStackView.orientation = .horizontal
        sessionSoundStackView.spacing = 8
        sessionSoundStackView.translatesAutoresizingMaskIntoConstraints = false
        sessionSoundStackView.addArrangedSubview(sessionSoundSwitch)
        sessionSoundStackView.addArrangedSubview(sessionSoundPopUp)

        // Кнопка тестирования звука сессии
        let sessionTestButton = NSButton()
        sessionTestButton.title = "▶️"
        sessionTestButton.bezelStyle = .circular
        sessionTestButton.target = self
        sessionTestButton.action = #selector(testSessionSound)
        sessionTestButton.translatesAutoresizingMaskIntoConstraints = false
        sessionTestButton.widthAnchor.constraint(equalToConstant: 30).isActive = true
        sessionTestButton.heightAnchor.constraint(equalToConstant: 30).isActive = true
        sessionSoundStackView.addArrangedSubview(sessionTestButton)

        sessionSoundContainer.addSubview(sessionSoundStackView)

        // Настройки звуков для завершения отдыхов
        let breakSoundContainer = createSettingContainer(
            iconName: "speaker.wave.2",
            iconColor: NSColor.systemBlue,
            title: "Звук завершения отдыха",
            subtitle: "Воспроизводить звук при завершении перерыва"
        )

        breakSoundSwitch = NSSwitch()
        breakSoundSwitch.state = SoundSettings.shared.isBreakSoundEnabled ? .on : .off
        breakSoundSwitch.target = self
        breakSoundSwitch.action = #selector(breakSoundToggled)
        breakSoundSwitch.translatesAutoresizingMaskIntoConstraints = false

        breakSoundPopUp = NSPopUpButton()
        breakSoundPopUp.translatesAutoresizingMaskIntoConstraints = false
        setupSoundPopUpButton(breakSoundPopUp, selectedSound: SoundSettings.shared.selectedBreakSound)
        breakSoundPopUp.target = self
        breakSoundPopUp.action = #selector(breakSoundChanged)

        let breakSoundStackView = NSStackView()
        breakSoundStackView.orientation = .horizontal
        breakSoundStackView.spacing = 8
        breakSoundStackView.translatesAutoresizingMaskIntoConstraints = false
        breakSoundStackView.addArrangedSubview(breakSoundSwitch)
        breakSoundStackView.addArrangedSubview(breakSoundPopUp)

        // Кнопка тестирования звука отдыха
        let breakTestButton = NSButton()
        breakTestButton.title = "▶️"
        breakTestButton.bezelStyle = .circular
        breakTestButton.target = self
        breakTestButton.action = #selector(testBreakSound)
        breakTestButton.translatesAutoresizingMaskIntoConstraints = false
        breakTestButton.widthAnchor.constraint(equalToConstant: 30).isActive = true
        breakTestButton.heightAnchor.constraint(equalToConstant: 30).isActive = true
        breakSoundStackView.addArrangedSubview(breakTestButton)

        breakSoundContainer.addSubview(breakSoundStackView)

        contentView.addSubview(sessionSoundContainer)
        contentView.addSubview(breakSoundContainer)

        // Настройка громкости звуков
        let volumeContainer = createSettingContainer(
            iconName: "speaker.wave.3",
            iconColor: NSColor.systemPurple,
            title: "Громкость уведомлений",
            subtitle: "Настройте громкость звуковых уведомлений"
        )

        soundVolumeSlider = NSSlider()
        soundVolumeSlider.translatesAutoresizingMaskIntoConstraints = false
        soundVolumeSlider.minValue = 0.0
        soundVolumeSlider.maxValue = 1.0
        soundVolumeSlider.doubleValue = Double(SoundSettings.shared.soundVolume)
        soundVolumeSlider.target = self
        soundVolumeSlider.action = #selector(volumeSliderChanged)
        soundVolumeSlider.isContinuous = true // Обновляем в реальном времени

        soundVolumeLabel = NSTextField()
        soundVolumeLabel.translatesAutoresizingMaskIntoConstraints = false
        soundVolumeLabel.isEditable = false
        soundVolumeLabel.isBordered = false
        soundVolumeLabel.backgroundColor = NSColor.clear
        soundVolumeLabel.textColor = NSColor.labelColor
        soundVolumeLabel.font = NSFont.systemFont(ofSize: 12)
        soundVolumeLabel.alignment = .center
        updateVolumeLabel()

        let volumeStackView = NSStackView()
        volumeStackView.orientation = .horizontal
        volumeStackView.spacing = 12
        volumeStackView.translatesAutoresizingMaskIntoConstraints = false
        volumeStackView.addArrangedSubview(soundVolumeSlider)
        volumeStackView.addArrangedSubview(soundVolumeLabel)

        contentView.addSubview(volumeContainer)
        volumeContainer.addSubview(volumeStackView)

        // Разделитель
        let separatorView = NSView()
        separatorView.wantsLayer = true
        separatorView.layer?.backgroundColor = NSColor.separatorColor.cgColor
        separatorView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(separatorView)

        // Настройка неформальных сессий
        let informalSessionContainer = createSettingContainer(
            iconName: "clock.badge.exclamationmark",
            iconColor: NSColor.systemGreen,
            title: "Напоминания без интервала",
            subtitle: "Предлагать отдых после 52+ минут работы без интервала"
        )

        informalSessionSwitch = NSSwitch()
        // Включен по умолчанию - если ключ не существует, устанавливаем true
        let isEnabled = UserDefaults.standard.object(forKey: "InformalSessionDetectorEnabled") as? Bool ?? true
        if UserDefaults.standard.object(forKey: "InformalSessionDetectorEnabled") == nil {
            UserDefaults.standard.set(true, forKey: "InformalSessionDetectorEnabled")
            UserDefaults.standard.synchronize() // Принудительно сохраняем
        }
        informalSessionSwitch.state = isEnabled ? .on : .off
        informalSessionSwitch.target = self
        informalSessionSwitch.action = #selector(informalSessionToggled)
        informalSessionSwitch.translatesAutoresizingMaskIntoConstraints = false

        // Кнопка тестирования
        let testButton = NSButton()
        testButton.title = "Тест"
        testButton.bezelStyle = .rounded
        testButton.target = self
        testButton.action = #selector(testInformalSession)
        testButton.translatesAutoresizingMaskIntoConstraints = false
        testButton.widthAnchor.constraint(equalToConstant: 50).isActive = true
        testButton.heightAnchor.constraint(equalToConstant: 28).isActive = true

        let informalControls = NSStackView()
        informalControls.orientation = .horizontal
        informalControls.spacing = 8
        informalControls.translatesAutoresizingMaskIntoConstraints = false
        informalControls.addArrangedSubview(informalSessionSwitch)
        informalControls.addArrangedSubview(testButton)

        informalSessionContainer.addSubview(informalControls)
        contentView.addSubview(informalSessionContainer)

        // Constraints
        NSLayoutConstraint.activate([
            sessionSoundContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 32),
            sessionSoundContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            sessionSoundContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            sessionSoundContainer.heightAnchor.constraint(equalToConstant: 60),

            sessionSoundStackView.centerYAnchor.constraint(equalTo: sessionSoundContainer.centerYAnchor),
            sessionSoundStackView.trailingAnchor.constraint(equalTo: sessionSoundContainer.trailingAnchor, constant: -16),

            breakSoundContainer.topAnchor.constraint(equalTo: sessionSoundContainer.bottomAnchor, constant: 16),
            breakSoundContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            breakSoundContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            breakSoundContainer.heightAnchor.constraint(equalToConstant: 60),

            breakSoundStackView.centerYAnchor.constraint(equalTo: breakSoundContainer.centerYAnchor),
            breakSoundStackView.trailingAnchor.constraint(equalTo: breakSoundContainer.trailingAnchor, constant: -16),

            volumeContainer.topAnchor.constraint(equalTo: breakSoundContainer.bottomAnchor, constant: 16),
            volumeContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            volumeContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            volumeContainer.heightAnchor.constraint(equalToConstant: 60),

            volumeStackView.centerYAnchor.constraint(equalTo: volumeContainer.centerYAnchor),
            volumeStackView.trailingAnchor.constraint(equalTo: volumeContainer.trailingAnchor, constant: -16),

            soundVolumeSlider.widthAnchor.constraint(equalToConstant: 150),
            soundVolumeLabel.widthAnchor.constraint(equalToConstant: 50),

            // Разделитель
            separatorView.topAnchor.constraint(equalTo: volumeContainer.bottomAnchor, constant: 24),
            separatorView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            separatorView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            separatorView.heightAnchor.constraint(equalToConstant: 1),

            // Неформальные сессии
            informalSessionContainer.topAnchor.constraint(equalTo: separatorView.bottomAnchor, constant: 24),
            informalSessionContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            informalSessionContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            informalSessionContainer.heightAnchor.constraint(equalToConstant: 60),

            informalControls.centerYAnchor.constraint(equalTo: informalSessionContainer.centerYAnchor),
            informalControls.trailingAnchor.constraint(equalTo: informalSessionContainer.trailingAnchor, constant: -16)
        ])

        // Обновляем состояние элементов управления
        updateSoundControlsState()

        tabViewItem.view = contentView
        tabView.addTabViewItem(tabViewItem)
    }

    // MARK: - Helper Methods



    // MARK: - Helper Methods

    private func createSettingContainer(iconName: String, iconColor: NSColor, title: String, subtitle: String) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0).cgColor
        container.layer?.cornerRadius = 12
        container.translatesAutoresizingMaskIntoConstraints = false

        // Иконка с SF Symbol
        let iconImageView = NSImageView()
        if let image = NSImage(systemSymbolName: iconName, accessibilityDescription: nil) {
            let config = NSImage.SymbolConfiguration(pointSize: 20, weight: .medium)
            iconImageView.image = image.withSymbolConfiguration(config)
            iconImageView.contentTintColor = iconColor
        }
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(iconImageView)

        // Заголовок
        let titleLabel = NSTextField(labelWithString: title)
        titleLabel.font = NSFont.systemFont(ofSize: 15, weight: .medium)
        titleLabel.textColor = NSColor.white
        titleLabel.backgroundColor = NSColor.clear
        titleLabel.isBordered = false
        titleLabel.isEditable = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(titleLabel)

        // Подзаголовок
        let subtitleLabel = NSTextField(labelWithString: subtitle)
        subtitleLabel.font = NSFont.systemFont(ofSize: 12)
        subtitleLabel.textColor = NSColor.secondaryLabelColor
        subtitleLabel.backgroundColor = NSColor.clear
        subtitleLabel.isBordered = false
        subtitleLabel.isEditable = false
        subtitleLabel.maximumNumberOfLines = 3 // Поддержка многострочного текста
        subtitleLabel.lineBreakMode = .byWordWrapping
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(subtitleLabel)

        // Constraints для элементов в контейнере
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
            iconImageView.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 24),
            iconImageView.heightAnchor.constraint(equalToConstant: 24),

            titleLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 12),

            subtitleLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 2),
            subtitleLabel.trailingAnchor.constraint(lessThanOrEqualTo: container.trailingAnchor, constant: -100)
        ])

        return container
    }

    // Создаем NumberStepperView для числовых значений
    private func createNumberStepper(value: Int, minValue: Int = 1, maxValue: Int = 999) -> NumberStepperView {
        let stepper = NumberStepperView(value: value, minValue: minValue, maxValue: maxValue)
        stepper.translatesAutoresizingMaskIntoConstraints = false
        stepper.heightAnchor.constraint(equalToConstant: 32).isActive = true
        stepper.widthAnchor.constraint(equalToConstant: 100).isActive = true  // Еще больше уменьшили
        return stepper
    }

    // Создаем контейнер с TextField для правильного центрирования
    private func createTextField(value: String, isEnabled: Bool = true) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.cornerRadius = 8
        container.layer?.borderWidth = 1
        container.layer?.borderColor = NSColor(red: 0.35, green: 0.35, blue: 0.35, alpha: 1.0).cgColor
        container.layer?.backgroundColor = NSColor(red: 46.0/255.0, green: 46.0/255.0, blue: 46.0/255.0, alpha: 1.0).cgColor  // #2E2E2E
        container.layer?.masksToBounds = true

        let textField = NSTextField()
        textField.stringValue = value
        textField.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        textField.alignment = .center
        textField.isEnabled = isEnabled
        textField.isEditable = isEnabled
        textField.isSelectable = true

        // Убираем все стандартные стили
        textField.bezelStyle = .squareBezel
        textField.isBordered = false
        textField.drawsBackground = false  // Фон у контейнера
        textField.focusRingType = .none
        textField.textColor = NSColor.white
        textField.backgroundColor = NSColor.clear

        textField.translatesAutoresizingMaskIntoConstraints = false
        container.translatesAutoresizingMaskIntoConstraints = false
        container.heightAnchor.constraint(equalToConstant: 32).isActive = true

        container.addSubview(textField)

        // Центрируем TextField в контейнере с отступами
        NSLayoutConstraint.activate([
            textField.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 8),
            textField.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -8),
            textField.topAnchor.constraint(equalTo: container.topAnchor, constant: 7),  // Отступ сверху
            textField.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -4)  // Отступ снизу
        ])

        return container
    }

    func setOnSettingsChanged(_ callback: @escaping (TimeInterval) -> Void) {
        self.onSettingsChanged = callback
    }

    func updateIntervalDuration(_ duration: TimeInterval) {
        workDurationField?.setValue(Int(duration / 60))
    }

    @objc private func launchAtLoginChanged() {
        LaunchAtLoginManager.shared.isEnabled = (launchAtLoginSwitch.state == .on)
        print("🚀 uProd: Launch at login changed to: \(LaunchAtLoginManager.shared.isEnabled)")
    }

    @objc private func adaptiveChangesToggled() {
        let adaptiveEnabled = adaptiveChangesSwitch.state == .on
        let fieldsEnabled = !adaptiveEnabled  // Поля активны когда адаптивный режим выключен

        // Включаем/отключаем NumberStepperView, но оставляем видимыми
        workDurationField.setEnabled(fieldsEnabled)
        shortBreakDurationField.setEnabled(fieldsEnabled)
        longBreakDurationField.setEnabled(fieldsEnabled)
        dailyIntervalsField.setEnabled(fieldsEnabled)
    }

    @objc private func windowDidBecomeKey() {
        // Принудительно обновляем переключатели при получении фокуса
        DispatchQueue.main.async {
            self.adaptiveChangesSwitch?.needsDisplay = true
            self.launchAtLoginSwitch?.needsDisplay = true
        }
    }

    @objc private func windowDidResignKey() {
        // Принудительно обновляем переключатели при потере фокуса
        DispatchQueue.main.async {
            self.adaptiveChangesSwitch?.needsDisplay = true
            self.launchAtLoginSwitch?.needsDisplay = true
        }
    }



    private func createWorkdaysContainer() -> NSView {
        let container = createSettingContainer(
            iconName: "calendar",
            iconColor: NSColor.systemGreen,
            title: "Рабочие дни",
            subtitle: "Выберите дни недели для работы"
        )

        let weekdays = ["Пн", "Вт", "Ср", "Чт", "Пт", "Сб", "Вс"]
        let workingDays = [true, true, true, true, true, false, false] // Пн-Пт рабочие

        let stackView = NSStackView()
        stackView.orientation = .horizontal
        stackView.spacing = 6
        stackView.translatesAutoresizingMaskIntoConstraints = false

        weekdayButtons.removeAll()

        for (index, day) in weekdays.enumerated() {
            let button = NSButton()
            button.title = day
            button.setButtonType(.toggle)
            button.bezelStyle = .rounded
            button.font = NSFont.systemFont(ofSize: 12, weight: .medium)
            button.state = workingDays[index] ? .on : .off
            button.translatesAutoresizingMaskIntoConstraints = false
            button.isBordered = false

            // Стилизация кнопки с более элегантным дизайном
            button.wantsLayer = true
            button.layer?.cornerRadius = 18
            button.layer?.backgroundColor = NSColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0).cgColor

            if workingDays[index] {
                // Рабочий день - тонкая синяя обводка и белый текст
                button.layer?.borderWidth = 2
                button.layer?.borderColor = NSColor.systemBlue.cgColor
                button.contentTintColor = NSColor.white
            } else {
                // Выходной - без обводки, серый текст
                button.layer?.borderWidth = 1
                button.layer?.borderColor = NSColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0).cgColor
                button.contentTintColor = NSColor.secondaryLabelColor
            }

            button.widthAnchor.constraint(equalToConstant: 36).isActive = true
            button.heightAnchor.constraint(equalToConstant: 36).isActive = true

            button.target = self
            button.action = #selector(weekdayButtonClicked(_:))

            weekdayButtons.append(button)
            stackView.addArrangedSubview(button)
        }

        container.addSubview(stackView)

        NSLayoutConstraint.activate([
            stackView.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            stackView.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16)
        ])

        return container
    }

    private func createPriorityProjectContainer() -> NSView {
        let container = createSettingContainer(
            iconName: "target",
            iconColor: NSColor.systemOrange,
            title: "Приоритетный проект (Ichiban)",
            subtitle: "Выберите главный проект для фокусировки"
        )

        priorityProjectPopUp = NSPopUpButton()
        priorityProjectPopUp.translatesAutoresizingMaskIntoConstraints = false
        priorityProjectPopUp.target = self
        priorityProjectPopUp.action = #selector(priorityProjectChanged)

        // Стилизация PopUp (без обводки, как у "Язык интерфейса")
        priorityProjectPopUp.wantsLayer = true
        priorityProjectPopUp.layer?.cornerRadius = 6
        priorityProjectPopUp.layer?.backgroundColor = NSColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0).cgColor

        container.addSubview(priorityProjectPopUp)

        NSLayoutConstraint.activate([
            priorityProjectPopUp.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            priorityProjectPopUp.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16),
            priorityProjectPopUp.widthAnchor.constraint(equalToConstant: 200)
        ])

        return container
    }

    @objc private func weekdayButtonClicked(_ sender: NSButton) {
        // Обновляем внешний вид кнопки с элегантным дизайном
        if sender.state == .on {
            // Рабочий день - синяя обводка
            sender.layer?.borderWidth = 2
            sender.layer?.borderColor = NSColor.systemBlue.cgColor
            sender.contentTintColor = NSColor.white
        } else {
            // Выходной - серая обводка
            sender.layer?.borderWidth = 1
            sender.layer?.borderColor = NSColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0).cgColor
            sender.contentTintColor = NSColor.secondaryLabelColor
        }
    }

    // MARK: - Sound Settings Methods

    private func setupSoundPopUpButton(_ popUpButton: NSPopUpButton, selectedSound: String) {
        popUpButton.removeAllItems()

        for sound in SoundManager.availableSounds {
            popUpButton.addItem(withTitle: sound.displayName)
            if let item = popUpButton.lastItem {
                item.representedObject = sound.fileName
            }
        }

        // Выбираем текущий звук
        for i in 0..<popUpButton.numberOfItems {
            if let item = popUpButton.item(at: i),
               let fileName = item.representedObject as? String,
               fileName == selectedSound {
                popUpButton.selectItem(at: i)
                break
            }
        }

        popUpButton.widthAnchor.constraint(equalToConstant: 140).isActive = true
    }

    private func updateSoundControlsState() {
        // Обновляем состояние выпадающих списков в зависимости от переключателей
        sessionSoundPopUp.isEnabled = sessionSoundSwitch.state == .on
        breakSoundPopUp.isEnabled = breakSoundSwitch.state == .on
    }

    @objc private func sessionSoundToggled() {
        SoundSettings.shared.isSessionSoundEnabled = (sessionSoundSwitch.state == .on)
        updateSoundControlsState()
    }

    @objc private func sessionSoundChanged() {
        if let selectedItem = sessionSoundPopUp.selectedItem,
           let fileName = selectedItem.representedObject as? String {
            SoundSettings.shared.selectedSessionSound = fileName

            // Воспроизводим звук для тестирования
            soundManager.playSound(fileName: fileName)
        }
    }

    @objc private func breakSoundToggled() {
        SoundSettings.shared.isBreakSoundEnabled = (breakSoundSwitch.state == .on)
        updateSoundControlsState()
    }

    @objc private func breakSoundChanged() {
        if let selectedItem = breakSoundPopUp.selectedItem,
           let fileName = selectedItem.representedObject as? String {
            SoundSettings.shared.selectedBreakSound = fileName

            // Воспроизводим звук для тестирования
            soundManager.playSound(fileName: fileName)
        }
    }

    @objc private func testSessionSound() {
        if let selectedItem = sessionSoundPopUp.selectedItem,
           let fileName = selectedItem.representedObject as? String {
            soundManager.playSound(fileName: fileName)
            NSLog("🔊 SettingsWindow: Тестируем звук сессии: \(fileName)")
        }
    }

    @objc private func volumeSliderChanged() {
        let newVolume = Float(soundVolumeSlider.doubleValue)
        SoundSettings.shared.soundVolume = newVolume
        updateVolumeLabel()
        NSLog("🔊 SettingsWindow: Громкость изменена: \(Int(newVolume * 100))%")
    }

    private func updateVolumeLabel() {
        let volumePercent = Int(SoundSettings.shared.soundVolume * 100)
        soundVolumeLabel.stringValue = "\(volumePercent)%"
    }

    @objc private func testBreakSound() {
        if let selectedItem = breakSoundPopUp.selectedItem,
           let fileName = selectedItem.representedObject as? String {
            soundManager.playSound(fileName: fileName)
            NSLog("🔊 SettingsWindow: Тестируем звук отдыха: \(fileName)")
        }
    }

    // MARK: - Informal Session Settings Methods

    @objc private func informalSessionToggled() {
        let isEnabled = (informalSessionSwitch.state == .on)
        UserDefaults.standard.set(isEnabled, forKey: "InformalSessionDetectorEnabled")

        // ИСПРАВЛЕНО: informalSessionDetector отключен, используем новую систему
        // if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
        //     appDelegate.informalSessionDetector.setEnabled(isEnabled)
        // }

        NSLog("⚙️ SettingsWindow: Предложения отдыха \(isEnabled ? "включены" : "выключены")")
    }

    // MARK: - Priority Project Methods

    @objc private func priorityProjectChanged() {
        guard let projectManager = projectManager else { return }

        let selectedIndex = priorityProjectPopUp.indexOfSelectedItem

        if selectedIndex == 0 {
            // "Нет приоритетного проекта"
            projectManager.setPriorityProject(nil)
            NSLog("🎯 SettingsWindow: Приоритетный проект сброшен")
        } else {
            // Получаем проект по индексу (учитываем, что первый элемент - "Нет")
            let activeProjects = projectManager.getActiveProjects()
            if selectedIndex - 1 < activeProjects.count {
                let selectedProject = activeProjects[selectedIndex - 1]

                // Проверяем, есть ли уже приоритетный проект
                if let currentPriorityProject = projectManager.getPriorityProject(),
                   currentPriorityProject.id != selectedProject.id {

                    // Показываем диалог подтверждения
                    let alert = NSAlert()
                    alert.messageText = "Смена приоритетного проекта"
                    alert.informativeText = "У вас уже есть приоритетный проект \"\(currentPriorityProject.name)\". Заменить его на \"\(selectedProject.name)\"?"
                    alert.addButton(withTitle: "Заменить")
                    alert.addButton(withTitle: "Отмена")
                    alert.alertStyle = .warning

                    let response = alert.runModal()
                    if response == .alertFirstButtonReturn {
                        // Пользователь подтвердил замену
                        projectManager.setPriorityProject(selectedProject)
                        NSLog("🎯 SettingsWindow: Заменен приоритетный проект: \(selectedProject.name)")
                    } else {
                        // Пользователь отменил, возвращаем предыдущий выбор
                        updatePriorityProjectPopUp()
                        return
                    }
                } else {
                    // Нет текущего приоритетного проекта или выбран тот же
                    projectManager.setPriorityProject(selectedProject)
                    NSLog("🎯 SettingsWindow: Установлен приоритетный проект: \(selectedProject.name)")
                }
            }
        }

        // Уведомляем AppDelegate об изменении приоритета
        NotificationCenter.default.post(name: NSNotification.Name("PriorityProjectChanged"), object: nil)
    }

    private func updatePriorityProjectPopUp() {
        guard let projectManager = projectManager else { return }

        priorityProjectPopUp.removeAllItems()

        // Добавляем опцию "Нет приоритетного проекта"
        priorityProjectPopUp.addItem(withTitle: "Нет приоритетного проекта")

        // Добавляем активные проекты
        let activeProjects = projectManager.getActiveProjects()
        for project in activeProjects {
            let emoji = project.customEmoji?.isEmpty == false ? project.customEmoji! : project.type.emoji
            let title = "\(emoji) \(project.name)"
            priorityProjectPopUp.addItem(withTitle: title)
        }

        // Устанавливаем текущий выбор
        if let priorityProject = projectManager.getPriorityProject(),
           let index = activeProjects.firstIndex(where: { $0.id == priorityProject.id }) {
            priorityProjectPopUp.selectItem(at: index + 1) // +1 из-за "Нет приоритетного проекта"
        } else {
            priorityProjectPopUp.selectItem(at: 0) // "Нет приоритетного проекта"
        }
    }

    @objc private func testInformalSession() {
        // РЕАЛИСТИЧНЫЙ ТЕСТ: Используем ту же логику что и в реальной жизни
        NSLog("🧪 SettingsWindow: Кнопка 'Тест' нажата!")
        print("🧪 SettingsWindow: Кнопка 'Тест' нажата!")
        logInfo("Settings", "🧪 Кнопка 'Тест неформального окна' нажата")

        if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
            NSLog("🧪 SettingsWindow: AppDelegate найден")
            print("🧪 SettingsWindow: AppDelegate найден")
            logInfo("Settings", "🧪 AppDelegate найден")

            // ИСПРАВЛЕНО: Сначала закрываем любые существующие окна
            if let existingWindow = appDelegate.modernCompletionWindow {
                logInfo("Settings", "🧪 Закрываем существующее окно перед тестом")
                existingWindow.orderOut(nil)
                appDelegate.modernCompletionWindow = nil
            }

            // ИСПРАВЛЕНО: informalSessionDetector отключен, используем новую систему напрямую
            // 1. Сбрасываем cooldown для возможности тестирования
            // appDelegate.informalSessionDetector.resetCooldown()
            logInfo("Settings", "🧪 Используем новую систему без старого детектора")

            // 2. Заполняем детектор тестовыми данными (52+ минуты активности)
            // appDelegate.informalSessionDetector.fillWithActiveMinutesForTesting()
            logInfo("Settings", "🧪 Пропускаем старый детектор, используем SimpleUnifiedSystem")

            // 3. НОВЫЙ ПОДХОД: Используем новую простую систему напрямую
            logInfo("Settings", "🧪 Вызываем showTestInformalWindow() с НОВОЙ простой системой")
            appDelegate.showTestInformalWindow()
            logInfo("Settings", "🧪 Показано тестовое окно с НОВОЙ системой")

            NSLog("🧪 SettingsWindow: Реалистичный тест неформального интервала запущен")
            print("🧪 SettingsWindow: Реалистичный тест неформального интервала запущен")
        } else {
            NSLog("❌ SettingsWindow: AppDelegate не найден!")
            print("❌ SettingsWindow: AppDelegate не найден!")
            logInfo("Settings", "❌ AppDelegate не найден!")
        }
    }


    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.alertStyle = .warning
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    func showWindow() {
        // Принудительно устанавливаем размер окна каждый раз
        let fixedSize = NSSize(width: 750, height: 600)
        self.setContentSize(fixedSize)
        self.minSize = fixedSize
        self.maxSize = fixedSize

        // Отключаем возможность изменения размера
        self.styleMask.remove(.resizable)

        self.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        // Еще раз устанавливаем размер после показа
        DispatchQueue.main.async {
            self.setContentSize(fixedSize)
        }
    }

    // MARK: - Temporary Settings Tab

    private func setupTemporaryTab(_ tabView: NSTabView) {
        let tabViewItem = NSTabViewItem()
        tabViewItem.label = "Временные"

        // Добавляем иконку для вкладки
        if let image = NSImage(systemSymbolName: "clock.badge", accessibilityDescription: nil) {
            let config = NSImage.SymbolConfiguration(pointSize: 14, weight: .medium)
            tabViewItem.image = image.withSymbolConfiguration(config)
        }

        let contentView = NSView()
        contentView.wantsLayer = true
        contentView.layer?.backgroundColor = NSColor(red: 0.15, green: 0.15, blue: 0.15, alpha: 1.0).cgColor

        // ПОЛНОСТЬЮ КОПИРУЕМ КОД из setupGeneralTab для позиционирования
        let positionContainer = createSettingContainer(
            iconName: "rectangle.on.rectangle",
            iconColor: NSColor.systemPurple,
            title: "Позиционирование окон",
            subtitle: "Выберите расположение окна раннего вовлечения"
        )

        let positionPopUp = NSPopUpButton()
        positionPopUp.addItems(withTitles: ["По правому краю", "Под иконкой"])
        positionPopUp.translatesAutoresizingMaskIntoConstraints = false
        positionPopUp.target = self
        positionPopUp.action = #selector(engagementPositionChanged)

        // Устанавливаем текущее значение
        let currentPosition = UserDefaults.standard.integer(forKey: "EarlyEngagementWindowPosition")
        positionPopUp.selectItem(at: currentPosition)

        positionContainer.addSubview(positionPopUp)
        contentView.addSubview(positionContainer)

        // ТОЧНО ТАКИЕ ЖЕ constraints как у languagePopUpButton
        NSLayoutConstraint.activate([
            positionContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 32),
            positionContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            positionContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            positionContainer.heightAnchor.constraint(equalToConstant: 60),

            positionPopUp.centerYAnchor.constraint(equalTo: positionContainer.centerYAnchor),
            positionPopUp.trailingAnchor.constraint(equalTo: positionContainer.trailingAnchor, constant: -16),
            positionPopUp.widthAnchor.constraint(equalToConstant: 120),
        ])

        tabViewItem.view = contentView
        tabView.addTabViewItem(tabViewItem)
    }

    @objc private func engagementPositionChanged(_ sender: NSPopUpButton) {
        let position = sender.indexOfSelectedItem
        UserDefaults.standard.set(position, forKey: "EarlyEngagementWindowPosition")
        logInfo("Settings", "🪟 Изменено позиционирование окна раннего вовлечения: \(position == 0 ? "правый край" : "под иконкой")")
    }
}
