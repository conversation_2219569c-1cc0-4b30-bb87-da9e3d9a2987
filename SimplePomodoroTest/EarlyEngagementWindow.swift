import Cocoa

/// Окно для показа сообщений системы раннего вовлечения
/// Наследует от IntervalNotificationWindow и адаптирует UI для специфических нужд
class EarlyEngagementWindow: NSWindow {
    
    // MARK: - Properties
    
    private var engagementMessage: EngagementMessage?
    private var onAccept: ((UUID?) -> Void)?
    private var onDecline: (() -> Void)?
    private var onSnooze: (() -> Void)?
    private var onFullSession: ((UUID?) -> Void)?

    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!

    // НОВАЯ СИСТЕМА: Динамические кнопки от ButtonMatrix
    private var dynamicButtons: [NSButton] = []
    private var buttonComponents: [ButtonMatrix.ButtonComponent] = []
    
    // MARK: - Initialization
    
    init() {
        // Создаем окно оптимального размера для кнопок
        let windowRect = NSRect(x: 0, y: 0, width: 500, height: 110)

        super.init(
            contentRect: windowRect,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        logInfo("EarlyEngagement", "🎨 EarlyEngagementWindow: Инициализация окна раннего вовлечения")
        setupWindow()
        setupUI()
        positionWindow()
    }

    // MARK: - Window Setup

    private func setupWindow() {
        logInfo("EarlyEngagement", "🎨 EarlyEngagementWindow: Настройка окна")
        
        // Настройки окна (аналогично IntervalNotificationWindow)
        self.level = .floating
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.ignoresMouseEvents = false
        self.hasShadow = true
        
        // Показываем на всех рабочих столах
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
    }
    
    private func positionWindow() {
        // Используем настройку пользователя для позиционирования
        let positionMode = UserDefaults.standard.integer(forKey: "EarlyEngagementWindowPosition")

        if positionMode == 1 {
            // Режим "под иконкой" - но без statusItemFrame, используем fallback
            positionUnderStatusItemFallback()
        } else {
            // Режим "по правому краю" (по умолчанию)
            positionAtRightEdge()
        }
    }

    /// Позиционирует окно по правому краю экрана (ТОЧНО как ModernCompletionWindow)
    private func positionAtRightEdge() {
        // ИСПРАВЛЕНО: Получаем позицию status item для правильного выравнивания по высоте
        // Используем тот же подход что и в AppDelegate для других окон
        if let appDelegate = NSApplication.shared.delegate as? NSObject,
           let statusItem = appDelegate.value(forKey: "statusItem") as? NSStatusItem,
           let statusButton = statusItem.button,
           let statusWindow = statusButton.window {

            let buttonFrame = statusButton.convert(statusButton.bounds, to: nil)
            let statusItemFrame = statusWindow.convertToScreen(buttonFrame)
            let windowFrame = self.frame

            // Позиционируем по правому краю, но на той же высоте что и ModernCompletionWindow
            let x = statusItemFrame.maxX + 20 // Смещение вправо от status item
            let y = statusItemFrame.minY - windowFrame.height - 8 // Тот же отступ что у ModernCompletionWindow

            self.setFrameOrigin(NSPoint(x: x, y: y))
        } else {
            // Fallback: используем старый метод
            if let screen = NSScreen.main {
                let screenFrame = screen.frame
                let windowFrame = self.frame

                let x = screenFrame.maxX - windowFrame.width - 10
                let y = screenFrame.maxY - windowFrame.height - 32

                self.setFrameOrigin(NSPoint(x: x, y: y))
            }
        }
    }

    /// Позиционирует окно под status item (fallback без точных координат)
    private func positionUnderStatusItemFallback() {
        if let screen = NSScreen.main {
            let screenFrame = screen.frame
            let windowFrame = self.frame

            // Примерное позиционирование в центре верхней части экрана с отступом как у ModernCompletionWindow
            let x = screenFrame.midX - windowFrame.width / 2
            let y = screenFrame.maxY - windowFrame.height - 32  // 24 (menu bar) + 8 (отступ ModernCompletionWindow)

            self.setFrameOrigin(NSPoint(x: x, y: y))
        }
    }

    /// Позиционирует окно под иконкой приложения в status bar (ТОЧНО как ModernCompletionWindow)
    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        let positionMode = UserDefaults.standard.integer(forKey: "EarlyEngagementWindowPosition")

        if positionMode == 1 {
            // Режим "под иконкой" - КОПИРУЕМ ТОЧНЫЙ КОД из ModernCompletionWindow.positionRelativeToStatusItem()
            let windowWidth = self.frame.width
            let windowHeight = self.frame.height

            // Позиционируем под status item с небольшим отступом
            let x = statusItemFrame.midX - windowWidth / 2
            let y = statusItemFrame.minY - windowHeight - 8

            self.setFrameOrigin(NSPoint(x: x, y: y))
        } else {
            // Режим "по правому краю" - игнорируем statusItemFrame
            positionAtRightEdge()
        }
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        logInfo("EarlyEngagement", "🎨 EarlyEngagementWindow: Настройка UI")

        // Создаем основной контейнер
        let containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Создаем сложный фон как в ModernCompletionWindow
        setupComplexBackground(for: containerView)

        self.contentView = containerView
        
        // Иконка раннего вовлечения (восход солнца)
        let iconView = createSunriseIcon()
        iconView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconView)
        
        // Заголовок
        titleLabel = NSTextField(labelWithString: "Время для работы!")
        titleLabel.font = NSFont.systemFont(ofSize: 14, weight: .semibold)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .left
        titleLabel.maximumNumberOfLines = 1
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        // Подзаголовок
        subtitleLabel = NSTextField(labelWithString: "Начнем с небольшого интервала?")
        subtitleLabel.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        subtitleLabel.textColor = NSColor.white.withAlphaComponent(0.9)
        subtitleLabel.alignment = .left
        subtitleLabel.maximumNumberOfLines = 2
        subtitleLabel.preferredMaxLayoutWidth = 250
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(subtitleLabel)
        
        // НОВАЯ СИСТЕМА: Кнопки будут создаваться динамически в showEngagementMessage
        // Пока создаем пустой массив, кнопки добавятся позже
        
        // Constraints
        NSLayoutConstraint.activate([
            
            // Иконка в левом верхнем углу
            iconView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 15),
            iconView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            iconView.widthAnchor.constraint(equalToConstant: 32),
            iconView.heightAnchor.constraint(equalToConstant: 32),
            
            // Заголовок рядом с иконкой
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 15),
            titleLabel.leadingAnchor.constraint(equalTo: iconView.trailingAnchor, constant: 12),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            
            // Подзаголовок под заголовком
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            subtitleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            
            // НОВАЯ СИСТЕМА: Кнопки будут добавлены динамически в showEngagementMessage
            // Пока устанавливаем базовый размер контейнера (уменьшен для компактности)
            containerView.bottomAnchor.constraint(greaterThanOrEqualTo: subtitleLabel.bottomAnchor, constant: 70)
        ])
        
        logInfo("EarlyEngagement", "🎨 EarlyEngagementWindow: UI настроен")
    }
    
    // MARK: - Dynamic Button Creation

    /// Создает динамические кнопки на основе ButtonMatrix
    private func createDynamicButtons(for message: EngagementMessage) {
        // Удаляем старые кнопки
        clearDynamicButtons()

        Logger.shared.log(.info, "EarlyEngagement", "🎨 ОТЛАДКА createDynamicButtons: message.buttonMatrix = \(message.buttonMatrix?.count ?? -1)")

        // НОВАЯ СИСТЕМА: Используем кнопки от ButtonMatrix если есть
        if let matrixButtons = message.buttonMatrix {
            Logger.shared.log(.info, "EarlyEngagement", "🎯 Используем кнопки от ButtonMatrix: \(matrixButtons.count)")
            for (index, button) in matrixButtons.enumerated() {
                Logger.shared.log(.info, "EarlyEngagement", "🎯   Кнопка \(index): '\(button.text)' (\(button.duration/60)мин)")
            }
            buttonComponents = matrixButtons
        } else {
            // Fallback: создаем базовый набор кнопок
            Logger.shared.log(.info, "EarlyEngagement", "⚠️ ButtonMatrix не найден, используем fallback")
            buttonComponents = [
                ButtonMatrix.ButtonComponent(
                    text: message.buttonText,
                    duration: message.proposedDuration,
                    type: .primary,
                    context: "primary_action"
                ),
                ButtonMatrix.ButtonComponent(
                    text: "Позже",
                    duration: 0,
                    type: .later,
                    context: "postpone"
                )
            ]

            // Если есть полная сессия, добавляем её
            if message.showFullSessionButton {
                buttonComponents.insert(
                    ButtonMatrix.ButtonComponent(
                        text: message.fullSessionButtonText,
                        duration: 52 * 60,
                        type: .fullSession,
                        context: "full_session"
                    ),
                    at: 1
                )
            }
        }

        // Создаем UI кнопки
        createButtonsUI()
    }

    /// Удаляет все динамические кнопки
    private func clearDynamicButtons() {
        for button in dynamicButtons {
            button.removeFromSuperview()
        }
        dynamicButtons.removeAll()
        buttonComponents.removeAll()
    }

    /// Создает UI для кнопок
    private func createButtonsUI() {
        guard let containerView = self.contentView else { return }

        let buttonHeight: CGFloat = 32
        let buttonSpacing: CGFloat = 12
        let sideMargin: CGFloat = 20

        // Создаем все кнопки
        for (index, component) in buttonComponents.enumerated() {
            let button = createStyledButton(title: component.text, isGreen: component.type == .primary)
            button.tag = index
            button.target = self
            button.action = #selector(dynamicButtonClicked(_:))

            containerView.addSubview(button)
            dynamicButtons.append(button)
        }

        // Настройка горизонтального layout
        setupHorizontalButtonLayout(
            buttons: dynamicButtons,
            containerView: containerView,
            buttonHeight: buttonHeight,
            buttonSpacing: buttonSpacing,
            sideMargin: sideMargin
        )

        // Фиксированный размер окна (оптимизирован для кнопок)
        self.setContentSize(NSSize(width: 500, height: 100))
    }

    /// Настраивает горизонтальное расположение кнопок
    private func setupHorizontalButtonLayout(
        buttons: [NSButton],
        containerView: NSView,
        buttonHeight: CGFloat,
        buttonSpacing: CGFloat,
        sideMargin: CGFloat
    ) {
        guard !buttons.isEmpty else { return }

        let totalButtons = buttons.count

        for (index, button) in buttons.enumerated() {
            button.translatesAutoresizingMaskIntoConstraints = false

            // Высота и вертикальное позиционирование
            NSLayoutConstraint.activate([
                button.heightAnchor.constraint(equalToConstant: buttonHeight),
                button.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 15)
            ])

            // Горизонтальное позиционирование с фиксированными размерами
            // Определяем ширину кнопок в зависимости от их типа/текста
            let buttonWidth = getButtonWidth(for: button, index: index, totalButtons: totalButtons)

            if totalButtons == 1 {
                // Одна кнопка - по центру
                NSLayoutConstraint.activate([
                    button.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
                    button.widthAnchor.constraint(equalToConstant: buttonWidth)
                ])
            } else if totalButtons == 2 {
                // Две кнопки с фиксированными отступами
                let fixedSpacing: CGFloat = 15

                if index == 0 {
                    NSLayoutConstraint.activate([
                        button.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: sideMargin),
                        button.widthAnchor.constraint(equalToConstant: buttonWidth)
                    ])
                } else {
                    NSLayoutConstraint.activate([
                        button.leadingAnchor.constraint(equalTo: buttons[0].trailingAnchor, constant: fixedSpacing),
                        button.widthAnchor.constraint(equalToConstant: buttonWidth)
                    ])
                }
            } else if totalButtons == 3 {
                // Три кнопки с фиксированными отступами
                let fixedSpacing: CGFloat = 12

                if index == 0 {
                    NSLayoutConstraint.activate([
                        button.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: sideMargin),
                        button.widthAnchor.constraint(equalToConstant: buttonWidth)
                    ])
                } else if index == 1 {
                    NSLayoutConstraint.activate([
                        button.leadingAnchor.constraint(equalTo: buttons[0].trailingAnchor, constant: fixedSpacing),
                        button.widthAnchor.constraint(equalToConstant: buttonWidth)
                    ])
                } else {
                    NSLayoutConstraint.activate([
                        button.leadingAnchor.constraint(equalTo: buttons[1].trailingAnchor, constant: fixedSpacing),
                        button.widthAnchor.constraint(equalToConstant: buttonWidth)
                    ])
                }
            }
        }
    }

    /// Определяет оптимальную ширину кнопки в зависимости от ее текста и позиции
    private func getButtonWidth(for button: NSButton, index: Int, totalButtons: Int) -> CGFloat {
        let buttonText = button.title.lowercased()

        // Определяем тип кнопки по тексту
        if buttonText.contains("начать") || buttonText.contains("work") || buttonText.contains("старт") {
            return 180  // Главная кнопка - самая широкая (увеличена для двузначных чисел)
        } else if buttonText.contains("максимальн") || buttonText.contains("full") || buttonText.contains("полн") {
            return 170  // Кнопка максимальной сессии - тоже широкая
        } else if buttonText.contains("позже") || buttonText.contains("later") || buttonText.contains("снуз") {
            return 90   // Кнопка "позже" - компактная
        } else {
            // Для неизвестных кнопок - средний размер
            return 120
        }
    }

    /// Обработчик нажатий на динамические кнопки
    @objc private func dynamicButtonClicked(_ sender: NSButton) {
        let index = sender.tag
        guard index < buttonComponents.count else { return }

        let component = buttonComponents[index]
        Logger.shared.log(.info, "EarlyEngagement", "🎯 Нажата кнопка: \(component.text) (тип: \(component.type.rawValue))")

        hideWindow()

        switch component.type {
        case .primary:
            let projectId = EarlyEngagementSystem.shared.getFocusedProject()?.id
            onAccept?(projectId)
        case .fullSession, .fullBar:
            let projectId = EarlyEngagementSystem.shared.getFocusedProject()?.id
            onFullSession?(projectId)
        case .later:
            onDecline?()
        case .snooze:
            onSnooze?()
        default:
            onDecline?()
        }
    }

    // MARK: - UI Creation Helpers

    private func createStyledButton(title: String, isGreen: Bool) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true

        // Создаем градиентный слой в стиле ModernCompletionWindow
        let gradientLayer = CAGradientLayer()

        if isGreen {
            // Зеленый градиент для основной кнопки (как в ModernCompletionWindow)
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.1, alpha: 1.0).cgColor
            ]
        } else {
            // Серый градиент для вторичных кнопок
            gradientLayer.colors = [
                NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0).cgColor,
                NSColor(red: 0.35, green: 0.35, blue: 0.4, alpha: 1.0).cgColor
            ]
        }

        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = 8

        // Тень для кнопки (как в ModernCompletionWindow)
        gradientLayer.shadowColor = NSColor.black.cgColor
        gradientLayer.shadowOpacity = 0.2
        gradientLayer.shadowOffset = CGSize(width: 0, height: 1)
        gradientLayer.shadowRadius = 3

        button.layer = gradientLayer

        // Белый текст (обычный шрифт)
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 13, weight: .regular)
            ]
        )

        return button
    }
    
    private func createSunriseIcon() -> NSView {
        let view = NSView()
        view.wantsLayer = true
        
        // Создаем иконку восхода солнца
        let iconLayer = CAShapeLayer()
        let iconPath = NSBezierPath()
        
        // Солнце (круг) - опущено на 5 пикселей
        let sunRect = NSRect(x: 8, y: 11, width: 16, height: 16)
        iconPath.appendOval(in: sunRect)

        // Лучи солнца
        let rayLength: CGFloat = 6
        let center = NSPoint(x: 16, y: 19)  // Центр тоже опущен на 5 пикселей
        
        // Верхний луч
        iconPath.move(to: NSPoint(x: center.x, y: center.y + 8))
        iconPath.line(to: NSPoint(x: center.x, y: center.y + 8 + rayLength))
        
        // Правый луч
        iconPath.move(to: NSPoint(x: center.x + 8, y: center.y))
        iconPath.line(to: NSPoint(x: center.x + 8 + rayLength, y: center.y))
        
        // Левый луч
        iconPath.move(to: NSPoint(x: center.x - 8, y: center.y))
        iconPath.line(to: NSPoint(x: center.x - 8 - rayLength, y: center.y))
        
        // Горизонт (линия) - тоже опущен на 5 пикселей
        iconPath.move(to: NSPoint(x: 2, y: 4))
        iconPath.line(to: NSPoint(x: 30, y: 4))
        
        iconLayer.path = iconPath.cgPath
        iconLayer.fillColor = NSColor.clear.cgColor
        iconLayer.strokeColor = NSColor.systemOrange.cgColor
        iconLayer.lineWidth = 2
        iconLayer.lineCap = .round
        iconLayer.lineJoin = .round
        
        view.layer?.addSublayer(iconLayer)
        return view
    }
    
    // MARK: - Public Methods
    
    /// Показывает окно с сообщением раннего вовлечения
    func showEngagementMessage(_ message: EngagementMessage,
                              onAccept: @escaping (UUID?) -> Void,
                              onDecline: @escaping () -> Void,
                              onSnooze: @escaping () -> Void,
                              onFullSession: @escaping (UUID?) -> Void) {

        self.engagementMessage = message
        self.onAccept = onAccept
        self.onDecline = onDecline
        self.onSnooze = onSnooze
        self.onFullSession = onFullSession

        // Обновляем текст
        titleLabel.stringValue = message.title
        subtitleLabel.stringValue = message.subtitle

        // НОВАЯ СИСТЕМА: Создаем кнопки от ButtonMatrix
        createDynamicButtons(for: message)

        Logger.shared.log(.info, "EarlyEngagement", "🎨 Показ сообщения: \(message.title), кнопок: \(buttonComponents.count)")
        showWindow()
    }

    private func showWindow() {
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: Показ окна с анимацией")
        
        // Начинаем с прозрачности
        self.alphaValue = 0
        self.makeKeyAndOrderFront(nil)
        
        // Анимация появления
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.4
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1
        })
        
        NSApp.activate(ignoringOtherApps: true)
    }
    
    private func hideWindow() {
        Logger.shared.log(.info, "EarlyEngagement", "🎨 EarlyEngagementWindow: Скрытие окна с анимацией")
        
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            self.animator().alphaValue = 0
            
            if let contentView = self.contentView {
                contentView.wantsLayer = true
                let transform = CATransform3DMakeScale(0.9, 0.9, 1)
                contentView.layer?.transform = transform
            }
        }) {
            self.orderOut(nil)
            
            // Возвращаем исходное состояние
            if let contentView = self.contentView {
                contentView.layer?.transform = CATransform3DIdentity
            }
            self.alphaValue = 1
        }
    }
    
    // MARK: - Button Actions (теперь используется dynamicButtonClicked)
    
    // MARK: - Window Properties
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }

    // MARK: - Background Setup

    private func setupComplexBackground(for view: NSView) {
        // Основной градиентный слой как в ModernCompletionWindow
        let mainGradient = CAGradientLayer()
        mainGradient.colors = [
            NSColor(red: 0.1, green: 0.1, blue: 0.15, alpha: 0.95).cgColor,
            NSColor(red: 0.15, green: 0.15, blue: 0.2, alpha: 0.95).cgColor
        ]
        mainGradient.startPoint = CGPoint(x: 0, y: 0)
        mainGradient.endPoint = CGPoint(x: 1, y: 1)
        mainGradient.cornerRadius = 12

        // Тень
        mainGradient.shadowColor = NSColor.black.cgColor
        mainGradient.shadowOpacity = 0.3
        mainGradient.shadowOffset = CGSize(width: 0, height: 4)
        mainGradient.shadowRadius = 12

        view.layer = mainGradient

        // ИСПРАВЛЕНИЕ: Устанавливаем frame для правильного скругления
        DispatchQueue.main.async {
            mainGradient.frame = view.bounds
            self.addRadialGradients(to: view)
        }
    }

    private func addRadialGradients(to view: NSView) {
        // ТОЧНАЯ КОПИЯ из BreakEndWindow - лесные цвета
        guard let layer = view.layer else { return }

        // Первый радиальный градиент (левый верхний) - Лесной зеленый 🌿
        let radial1 = CAGradientLayer()
        radial1.type = .radial
        radial1.colors = [
            NSColor(red: 0.4, green: 0.7, blue: 0.3, alpha: 0.3).cgColor,  // Лесной зеленый
            NSColor.clear.cgColor
        ]
        radial1.startPoint = CGPoint(x: 0.2, y: 0.8)
        radial1.endPoint = CGPoint(x: 0.8, y: 0.2)
        radial1.frame = layer.bounds
        radial1.cornerRadius = 12  // ИСПРАВЛЕНИЕ: Скругляем радиальные градиенты
        layer.addSublayer(radial1)

        // Второй радиальный градиент (правый нижний) - Изумрудный 💎
        let radial2 = CAGradientLayer()
        radial2.type = .radial
        radial2.colors = [
            NSColor(red: 0.2, green: 0.6, blue: 0.4, alpha: 0.2).cgColor,  // Изумрудный
            NSColor.clear.cgColor
        ]
        radial2.startPoint = CGPoint(x: 0.8, y: 0.2)
        radial2.endPoint = CGPoint(x: 0.2, y: 0.8)
        radial2.frame = layer.bounds
        radial2.cornerRadius = 12  // ИСПРАВЛЕНИЕ: Скругляем радиальные градиенты
        layer.addSublayer(radial2)
    }
}
