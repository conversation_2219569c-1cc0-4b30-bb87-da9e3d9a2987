import Cocoa
import Foundation

/// Централизованный детектор событий сна/пробуждения системы
/// Различает реальный сон компьютера от длительной неактивности пользователя
class SleepWakeDetector {
    static let shared = SleepWakeDetector()
    
    // MARK: - Types
    
    /// Типы событий сна/пробуждения
    enum SleepWakeEvent {
        case willSleep                                    // Система засыпает
        case didWake(duration: TimeInterval, wasRealSleep: Bool)  // Система проснулась
    }
    
    /// Тип события для различения сна и неактивности
    enum EventType {
        case realSleep      // Реальный сон компьютера (NSWorkspace события)
        case longInactivity // Длительная неактивность пользователя
        case shortBreak     // Короткий перерыв (< 5 минут)
    }
    
    // MARK: - Properties
    
    /// Колбэк для уведомления о событиях сна/пробуждения
    var onSleepWakeEvent: ((SleepWakeEvent) -> Void)?
    
    /// Время начала сна
    private var sleepStartTime: Date?
    
    /// Время последней активности перед сном
    private var lastActivityBeforeSleep: Date?
    
    /// Флаг получения NSWorkspace уведомления о сне
    private var receivedSleepNotification = false
    
    /// Флаг активности мониторинга
    private var isMonitoring = false
    
    // MARK: - Initialization
    
    private init() {
        // Singleton
    }
    
    // MARK: - Public Methods
    
    /// Начинает мониторинг событий сна/пробуждения
    func startMonitoring() {
        guard !isMonitoring else {
            print("🌙 SleepWakeDetector: Мониторинг уже активен")
            return
        }

        setupSleepWakeNotifications()
        isMonitoring = true
        print("🌙 SleepWakeDetector: Мониторинг событий сна запущен")
    }
    
    /// Останавливает мониторинг событий сна/пробуждения
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        removeSleepWakeNotifications()
        isMonitoring = false
        print("🌙 SleepWakeDetector: Мониторинг событий сна остановлен")
    }
    
    /// Возвращает информацию о текущем состоянии
    func getDebugInfo() -> String {
        let sleepStatus = sleepStartTime != nil ? "спит с \(sleepStartTime!)" : "не спит"
        let monitoringStatus = isMonitoring ? "активен" : "неактивен"
        
        return """
        🌙 SleepWakeDetector:
           Мониторинг: \(monitoringStatus)
           Состояние: \(sleepStatus)
           Получено уведомление о сне: \(receivedSleepNotification)
        """
    }
    
    // MARK: - Testing Methods
    
    /// Симулирует событие засыпания системы (для тестирования)
    func simulateSystemSleep() {
        print("🌙 SleepWakeDetector: 🧪 Симуляция засыпания системы")
        systemWillSleep()
    }
    
    /// Симулирует событие пробуждения системы (для тестирования)
    func simulateSystemWake(afterDuration duration: TimeInterval = 0) {
        print("🌙 SleepWakeDetector: 🧪 Симуляция пробуждения системы")
        
        if duration > 0 {
            // Устанавливаем время сна для симуляции
            sleepStartTime = Date().addingTimeInterval(-duration)
        }
        
        systemDidWake()
    }
    
    /// Симулирует длительную неактивность без сна (для тестирования)
    func simulateLongInactivity(duration: TimeInterval) {
        print("🌙 SleepWakeDetector: 🧪 Симуляция длительной неактивности (\(Int(duration/60)) мин)")
        
        // НЕ устанавливаем receivedSleepNotification = true
        // Это имитирует ситуацию когда пользователь просто долго не был активен
        sleepStartTime = Date().addingTimeInterval(-duration)
        systemDidWake()
    }
    
    // MARK: - Private Methods
    
    private func setupSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemWillSleep),
            name: NSWorkspace.willSleepNotification,
            object: nil
        )
        
        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemDidWake),
            name: NSWorkspace.didWakeNotification,
            object: nil
        )
    }
    
    private func removeSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.removeObserver(
            self,
            name: NSWorkspace.willSleepNotification,
            object: nil
        )
        NSWorkspace.shared.notificationCenter.removeObserver(
            self,
            name: NSWorkspace.didWakeNotification,
            object: nil
        )
    }
    
    @objc private func systemWillSleep() {
        print("🌙 SleepWakeDetector: 💤 СИСТЕМА ЗАСЫПАЕТ")
        
        sleepStartTime = Date()
        lastActivityBeforeSleep = Date() // Предполагаем что была активность перед сном
        receivedSleepNotification = true
        
        // Уведомляем подписчиков
        onSleepWakeEvent?(.willSleep)
    }
    
    @objc private func systemDidWake() {
        print("🌙 SleepWakeDetector: 🌅 СИСТЕМА ПРОСНУЛАСЬ")

        // Определяем длительность сна/неактивности
        let duration: TimeInterval
        if let sleepStart = sleepStartTime {
            duration = Date().timeIntervalSince(sleepStart)
        } else {
            duration = 0
        }

        let minutes = Int(duration / 60)
        print("🌙 SleepWakeDetector: Длительность: \(minutes) минут")

        // Определяем тип события
        let wasRealSleep = determineSleepType(duration: duration)
        let eventType: EventType = wasRealSleep ? .realSleep :
                                  (duration < 5 * 60 ? .shortBreak : .longInactivity)

        print("🌙 SleepWakeDetector: Тип события: \(eventType)")

        // ИНТЕГРАЦИЯ С СИСТЕМОЙ РАННЕГО ВОВЛЕЧЕНИЯ
        // ИСПРАВЛЕНО: Не показываем сразу, а ждем активности пользователя
        if wasRealSleep && duration > 30 * 60 { // Только для реального сна > 30 минут
            EarlyEngagementSystem.shared.startWaitingForActivity(sleepDuration: duration, wakeTime: Date())
            print("🌙 SleepWakeDetector: 🌅 Ожидаем активности пользователя для показа сообщения")
        }

        // Уведомляем подписчиков
        onSleepWakeEvent?(.didWake(duration: duration, wasRealSleep: wasRealSleep))

        // Сбрасываем состояние
        sleepStartTime = nil
        lastActivityBeforeSleep = nil
        receivedSleepNotification = false
    }
    
    /// Определяет был ли это реальный сон или длительная неактивность
    private func determineSleepType(duration: TimeInterval) -> Bool {
        // 1. Очень короткие периоды (< 5 мин) - точно не сон
        if duration < 5 * 60 {
            print("🌙 SleepWakeDetector: Короткий период (\(Int(duration/60)) мин) - не сон")
            return false
        }
        
        // 2. Если получили NSWorkspace уведомление - это реальный сон
        if receivedSleepNotification {
            print("🌙 SleepWakeDetector: Получено NSWorkspace уведомление - реальный сон")
            return true
        }
        
        // 3. Очень длинные периоды (> 2 часа) без уведомления - скорее всего сон
        if duration > 2 * 60 * 60 {
            print("🌙 SleepWakeDetector: Очень длинный период (\(Int(duration/60)) мин) - вероятно сон")
            return true
        }
        
        // 4. Анализ времени суток (ночные часы = больше вероятность сна)
        let hour = Calendar.current.component(.hour, from: Date())
        let isNightTime = hour >= 22 || hour <= 6
        
        if isNightTime && duration > 30 * 60 {
            print("🌙 SleepWakeDetector: Ночное время + длительность > 30 мин - вероятно сон")
            return true
        }
        
        // 5. По умолчанию считаем длительной неактивностью
        print("🌙 SleepWakeDetector: Определено как длительная неактивность")
        return false
    }
}
